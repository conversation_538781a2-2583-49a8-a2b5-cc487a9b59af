using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IONE : IProcess
    {
        private readonly string SearchMainPage = "https://ecomm.one-line.com/one-ecom";

        public IONE() : base("ONE", "ONEY") { }


        public override List<IProcessItem> Run(string searchKey, string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "点击", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "输入", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "点击", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "批量处理集装箱", IProcessType.OPERATE);
            IProcessItem sixPro = new IProcessItem(5, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "let trackTab = document.getElementById('quick-action_tracking-box');trackTab.click();";
            thiPro.JScript = "let input = document.querySelector('textarea');if(input){input.focus(); document.execCommand('insertText', false, '" + searchKey + "');}";
            fouPro.JScript = "var buttons = document.querySelectorAll('button');" +
                        "buttons.forEach(function(button) {" +
                        "button.classList.forEach(function(clazz){" +
                        "if(clazz.includes('TrackInputAreaContent_track-button')){" +
                        "button.click();" +
                        "return" +
                        "}" +
                        "})" +
                        "});";

            // 批量处理所有集装箱：依次点击每个集装箱并收集信息
            fifPro.JScript = @"(function() {
                        const iframe = document.getElementById('IframeCurrentEcom');
                        if(iframe){
                            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                            const table = iframeDocument.getElementById('main-grid');
                            if (table) {
                                // 等待表格加载完成
                                const observer = new MutationObserver((mutationsList, observer) => {
                                    if (table.rows.length >= 2) {
                                        observer.disconnect();
                                        processAllContainers(table);
                                    }
                                });
                                observer.observe(table, { childList: true, subtree: true });
                                if (table.rows.length >= 2) {
                                    observer.disconnect();
                                    processAllContainers(table);
                                }
                            }

                            async function processAllContainers(table) {
                                window.allContainerData = [];

                                // 收集所有集装箱链接
                                const containerLinks = [];
                                for(let i = 1; i < table.rows.length; i++) {
                                    const row = table.rows[i];
                                    const link = row.querySelector('a');
                                    if(link) {
                                        containerLinks.push({
                                            element: link,
                                            text: link.textContent.trim(),
                                            index: i
                                        });
                                    }
                                }

                                console.log('找到集装箱数量:', containerLinks.length);

                                // 依次点击每个集装箱并收集详细信息
                                for(let i = 0; i < containerLinks.length; i++) {
                                    const linkInfo = containerLinks[i];
                                    console.log('处理集装箱:', linkInfo.text);

                                    // 点击集装箱链接
                                    linkInfo.element.click();

                                    // 等待详细信息加载
                                    await new Promise(resolve => setTimeout(resolve, 2000));

                                    // 收集当前集装箱的详细信息
                                    const containerDetail = {
                                        containerNo: linkInfo.text,
                                        detailHtml: iframeDocument.documentElement.innerHTML,
                                        index: i
                                    };

                                    window.allContainerData.push(containerDetail);
                                    console.log('已收集集装箱信息:', linkInfo.text);
                                }

                                console.log('所有集装箱信息收集完成，总数:', window.allContainerData.length);
                            }
                        }
                    })()";

            sixPro.JScript = @"(function() {
                        // 返回所有收集到的集装箱数据
                        if (window.allContainerData && window.allContainerData.length > 0) {
                            return JSON.stringify(window.allContainerData);
                        } else {
                            // 如果没有收集到数据，返回当前iframe内容
                            var iframe = document.getElementById('IframeCurrentEcom');
                            if (iframe) {
                                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                                return iframeDocument.documentElement.innerHTML;
                            }
                            return '';
                        }
                    })()";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);
            processList.Add(sixPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();

                // 检查是否是JSON格式的多集装箱数据
                if (resultString.StartsWith("[") && resultString.Contains("containerNo"))
                {
                    // 处理多集装箱JSON数据
                    result = ProcessMultipleContainers(resultString);
                }
                else
                {
                    // 处理单个集装箱的HTML数据
                    try
                    {
                        result.ETA = this.GetETA(resultString);
                    }
                    catch (Exception exc)
                    {
                        result.ETAExc = exc.Message;
                    }
                    try
                    {
                        result.ContainerList = this.GetContainerItems(resultString);
                    }
                    catch (Exception)
                    {
                        throw;
                    }
                }

                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private IResult ProcessMultipleContainers(string jsonData)
        {
            try
            {
                IResult result = new IResult();
                BindingList<IResultContainer> allContainers = new BindingList<IResultContainer>();

                // 解析JSON数据
                JArray containerArray = JArray.Parse(jsonData);

                foreach (JObject containerObj in containerArray)
                {
                    string containerNo = containerObj["containerNo"]?.ToString() ?? "";
                    string detailHtml = containerObj["detailHtml"]?.ToString() ?? "";

                    if (!string.IsNullOrEmpty(detailHtml))
                    {
                        try
                        {
                            // 从每个集装箱的详细HTML中提取信息
                            var containerInfo = ExtractContainerInfo(detailHtml, containerNo);
                            if (containerInfo != null)
                            {
                                allContainers.Add(containerInfo);
                            }

                            // 提取ETA信息（使用第一个有效的ETA）
                            if (string.IsNullOrEmpty(result.ETA))
                            {
                                try
                                {
                                    result.ETA = this.GetETA(detailHtml);
                                }
                                catch
                                {
                                    // 忽略ETA提取错误，继续处理其他集装箱
                                }
                            }
                        }
                        catch (Exception exc)
                        {
                            // 记录错误但继续处理其他集装箱
                            Console.WriteLine($"处理集装箱 {containerNo} 时出错: {exc.Message}");
                        }
                    }
                }

                result.ContainerList = allContainers;
                return result;
            }
            catch (Exception exc)
            {
                throw new Exception($"处理多集装箱数据时出错: {exc.Message}");
            }
        }

        private IResultContainer ExtractContainerInfo(string html, string containerNo)
        {
            try
            {
                IResultContainer container = new IResultContainer();

                // 提取集装箱相关的时间信息
                string pickupDate = "";
                string unloadingDate = "";
                string returnDate = "";

                // 查找detailInfo div中的表格
                string detailInfoPattern = @"<div id=""detailInfo""[^>]*>(.*?)</div>\s*(?=<div|$)";
                Match detailMatch = Regex.Match(html, detailInfoPattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);

                if (detailMatch.Success)
                {
                    string detailContent = detailMatch.Value;

                    // 查找id="detail"的表格
                    string tablePattern = @"<table[^>]*id=""detail""[^>]*>(.*?)</table>";
                    Match tableMatch = Regex.Match(detailContent, tablePattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);

                    if (tableMatch.Success)
                    {
                        string tableContent = tableMatch.Value;

                        // 解析表格行
                        string rowPattern = @"<tr[^>]*>(.*?)</tr>";
                        MatchCollection rowMatches = Regex.Matches(tableContent, rowPattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);

                        foreach (Match rowMatch in rowMatches)
                        {
                            string rowContent = rowMatch.Value;

                            // 提取单元格内容
                            string cellPattern = @"<td[^>]*>(.*?)</td>";
                            MatchCollection cellMatches = Regex.Matches(rowContent, cellPattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);

                            if (cellMatches.Count >= 4)
                            {
                                string statusCell = cellMatches[1].Value;
                                string dateCell = cellMatches[3].Value;

                                // 清理HTML标签获取纯文本
                                string status = Regex.Replace(statusCell, @"<[^>]+>", "").Trim();
                                string dateText = Regex.Replace(dateCell, @"<[^>]+>", "").Trim();

                                // 提取日期 (格式: 2025-07-22 19:07)
                                string datePattern = @"(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2})";
                                Match dateMatch = Regex.Match(dateText, datePattern);

                                if (dateMatch.Success)
                                {
                                    string extractedDate = dateMatch.Groups[1].Value;

                                    // 根据状态分类日期
                                    if (status.Contains("Empty Container Returned from Customer"))
                                    {
                                        returnDate = extractedDate;
                                    }
                                    else if (status.Contains("Gate Out from Inbound Terminal for Delivery to Consignee") ||
                                             status.Contains("Gate Out from Inbound Terminal for Delivery"))
                                    {
                                        pickupDate = extractedDate;
                                    }
                                    else if (status.Contains("Unloaded from"))
                                    {
                                        unloadingDate = extractedDate;
                                    }
                                }
                            }
                        }
                    }
                }

                // 使用SetNewContainerItem方法设置集装箱信息
                container.SetNewContainerItem(containerNo, pickupDate, unloadingDate, returnDate);

                return container;
            }
            catch (Exception exc)
            {
                throw new Exception($"提取集装箱 {containerNo} 信息时出错: {exc.Message}");
            }
        }




        private string GetETA(string result)
        {
            try
            {
                string contentPattern = @"<table[^>]*id=""[^""]*sailing""[^>]*>(.*?)<\/table>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<tr>(.*?)<\/tr>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = trMatches[trMatches.Count - 1].Value;

                string tdPattern = @"<td[^>]*>(.*?)<\/td>";

                MatchCollection tdMatches = Regex.Matches(matchVal, tdPattern, RegexOptions.Singleline);

                if (tdMatches.Count < 5)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = tdMatches[4].Value;
                string etaPattern = @"<\/span>(.*?)<\/td>";

                MatchCollection etaMatches = Regex.Matches(matchVal, etaPattern, RegexOptions.Singleline);

                if (etaMatches.Count <= 0 || etaMatches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = etaMatches[0].Groups[1].Value;

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                if (DateTime.TryParse(inputDate, out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }



        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerItemList = new BindingList<IResultContainer>();

                // 对于单个集装箱的情况，尝试从HTML中提取集装箱号
                string containerNo = ExtractContainerNumber(containerString);

                if (!string.IsNullOrEmpty(containerNo))
                {
                    // 提取集装箱信息
                    var containerInfo = ExtractContainerInfo(containerString, containerNo);
                    if (containerInfo != null)
                    {
                        containerItemList.Add(containerInfo);
                    }
                }

                return containerItemList;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private string ExtractContainerNumber(string htmlContent)
        {
            try
            {
                // 尝试从HTML中提取集装箱号
                // 这里可能需要根据实际的页面结构来调整

                // 方法1: 查找常见的集装箱号格式 (4个字母 + 7个数字)
                string containerPattern = @"\b[A-Z]{4}\d{7}\b";
                Match containerMatch = Regex.Match(htmlContent, containerPattern);

                if (containerMatch.Success)
                {
                    return containerMatch.Value;
                }

                // 方法2: 如果有特定的HTML结构包含集装箱号，可以在这里添加

                return "";
            }
            catch
            {
                return "";
            }
        }


    }
}
